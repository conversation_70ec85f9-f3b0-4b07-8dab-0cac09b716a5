import { Entity, GameMode, Player, Vector3 } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";

/**
 * Constants for Vita Mimic behavior configuration
 */
const DARKNESS_DURATION = 100; // 5 seconds (20 ticks per second)
const RAYCAST_LENGTH = 128;
const RAYCAST_STEP = 2;
const RAYCAST_RADIUS = 5;

/**
 * Handles the effects applied to players when they start looking at Vita Mimic.
 * This function is triggered by the looked_at component and checks all players within 128 blocks
 * using fixed length raycast with 2-block steps and 5-block radius detection per raycast point.
 * Applies darkness effect for 15 seconds when players look at the mimic.
 *
 * @param vitaMimic - The Vita Mimic entity
 */
export function vitaMimicOnPlayerStartLooking(vitaMimic: Entity): void {
  try {
    const vitaMimicLocation: Vector3 = vitaMimic.location;

    // Get all valid players within 128 blocks
    const players: Player[] = vitaMimic.dimension.getPlayers({
      location: vitaMimicLocation,
      maxDistance: 128,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    for (const player of players) {
      if (isPlayerLookingAtVitaMimic(player, vitaMimicLocation)) {
        // Apply darkness effect for 15 seconds
        player.addEffect("darkness", DARKNESS_DURATION, {
          amplifier: 0,
          showParticles: false
        });
      }
    }
  } catch (error) {
    console.warn(`Failed to apply Vita Mimic look effects: ${error}`);
  }
}

/**
 * Checks if a player's view direction is pointing at Vita Mimic's location using fixed length raycast.
 * Uses raycast with 2-block steps and checks for 5-block radius around each raycast point.
 * The detection area extends +3 blocks upward in Y direction from Vita Mimic's location.
 *
 * @param player - The player to check
 * @param vitaMimicLocation - Vita Mimic's current location
 * @returns True if the player is looking at Vita Mimic's area
 */
function isPlayerLookingAtVitaMimic(player: Player, vitaMimicLocation: Vector3): boolean {
  try {
    const playerLocation: Vector3 = player.getHeadLocation();
    const viewDirection: Vector3 = player.getViewDirection();
    const maxDistance = RAYCAST_LENGTH;
    const raycastStep = RAYCAST_STEP;
    const detectionRadius = RAYCAST_RADIUS;

    // Perform fixed length raycast from player's head location
    const raycastPoints: Vector3[] = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);

    // Check each raycast point for proximity to Vita Mimic
    for (const rayPoint of raycastPoints) {
      const distanceToVitaMimic = getDistance(rayPoint, vitaMimicLocation);
      
      if (distanceToVitaMimic <= detectionRadius) {
        // Check if the raycast point is within the vertical detection area (3 blocks high)
        if (rayPoint.y >= vitaMimicLocation.y && rayPoint.y <= vitaMimicLocation.y + 3.0) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}
