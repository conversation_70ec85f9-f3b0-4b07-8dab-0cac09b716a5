import { <PERSON><PERSON><PERSON>, Player, Vector3, GameMode, EntityDamageCause } from "@minecraft/server";
import { getDistance } from "../utilities/vector3";
import { fixedLenRaycast } from "../utilities/raycasts";

/**
 * @fileoverview Werewolf Entity Handler for DitSH Add-On
 *
 * This module handles the werewolf entity behavior including:
 * - Teleportation and damage to players looking at it
 * - Player detection and line-of-sight checking
 *
 * <AUTHOR>
 * @version 1.0.0
 */

/** Damage dealt to players when werewolf teleports to them (10 hearts = 20 damage) */
const WEREWOLF_DAMAGE: number = 20;

/** Detection radius for players (128 blocks) */
const DETECTION_RADIUS: number = 128;

/** Raycast parameters for line-of-sight detection */
const RAYCAST_RADIUS: number = 5;
const RAYCAST_STEP: number = 2;

/**
 * Checks if a player is looking at the werewolf using fixed length raycast.
 * Uses 2-block step size and 5-block radius detection per raycast point.
 * 
 * @param player - The player to check
 * @param werewolfLocation - The werewolf's current location
 * @returns True if the player is looking at the werewolf
 */
function isPlayerLookingAt<PERSON>erewolf(player: Player, werewolfLocation: Vector3): boolean {
  try {
    const playerLocation: Vector3 = {
      x: player.location.x,
      y: player.location.y + 1.6, // Eye level
      z: player.location.z
    };

    const viewDirection: Vector3 = player.getViewDirection();
    const maxDistance: number = getDistance(playerLocation, werewolfLocation);
    const raycastStep: number = RAYCAST_STEP;
    const detectionRadius: number = RAYCAST_RADIUS;

    // Perform fixed length raycast from player's head location
    const raycastPoints: Vector3[] = fixedLenRaycast(playerLocation, viewDirection, maxDistance, raycastStep);

    // Check each raycast point for proximity to werewolf
    for (const rayPoint of raycastPoints) {
      const distanceToWerewolf: number = getDistance(rayPoint, werewolfLocation);
      
      if (distanceToWerewolf <= detectionRadius) {
        // Check if the raycast point is within the vertical detection area (3 blocks high)
        if (rayPoint.y >= werewolfLocation.y && rayPoint.y <= werewolfLocation.y + 3.0) {
          return true;
        }
      }
    }

    return false;
  } catch (error) {
    return false;
  }
}

/**
 * Handles werewolf teleportation to a player who is looking at it.
 * Teleports directly to the player and immediately deals 10 hearts of damage.
 * 
 * @param werewolf - The werewolf entity
 */
export function werewolfTeleportAndKill(werewolf: Entity): void {
  try {
    const werewolfLocation: Vector3 = werewolf.location;

    // Get all valid players within detection radius
    const players: Player[] = werewolf.dimension.getPlayers({
      location: werewolfLocation,
      maxDistance: DETECTION_RADIUS,
      excludeGameModes: [GameMode.Creative, GameMode.Spectator]
    });

    // Find the first player looking at the werewolf
    for (const player of players) {
      if (isPlayerLookingAtWerewolf(player, werewolfLocation)) {
        // Teleport werewolf directly to the player
        werewolf.teleport(player.location);

        // Immediately damage the player for 10 hearts
        player.applyDamage(WEREWOLF_DAMAGE, {
          cause: EntityDamageCause.entityAttack,
          damagingEntity: werewolf
        });

        // Play kill sound effect
        werewolf.dimension.playSound("mob.ditsh.werewolf.kill", player.location);

        // Only teleport to the first player found looking
        break;
      }
    }
  } catch (error) {
    console.warn(`Failed to handle werewolf teleport and kill: ${error}`);
  }
}