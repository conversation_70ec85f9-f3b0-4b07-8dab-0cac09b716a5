{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:vita_mimic", "is_spawnable": true, "is_summonable": true, "properties": {"ditsh:playing_music": {"type": "bool", "client_sync": false, "default": false}}}, "component_groups": {"ditsh:active": {"minecraft:looked_at": {"search_radius": 128.0, "look_at_locations": [{"location": "head"}, {"location": "body"}, {"location": "feet", "vertical_offset": 0.5}], "set_target": "never", "find_players_only": true, "looked_at_cooldown": 0.1, "field_of_view": 5, "scale_fov_by_distance": false, "line_of_sight_obstruction_type": "collision_for_camera", "looked_at_event": {"event": "ditsh:on_player_start_looking", "target": "self"}, "filters": {"test": "actor_health", "subject": "other", "operator": ">", "value": 0}}}, "ditsh:music_playing": {"minecraft:timer": {"looping": true, "time": 118, "time_down_event": {"event": "ditsh:maintain_chase_music"}}}}, "events": {"minecraft:entity_spawned": {"add": {"component_groups": ["ditsh:active"]}}, "ditsh:on_death": {}, "ditsh:start_chase_music": {"add": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": true}}, "ditsh:stop_chase_music": {"remove": {"component_groups": ["ditsh:music_playing"]}, "set_property": {"ditsh:playing_music": false}}, "ditsh:maintain_chase_music": {}, "ditsh:on_kill": {"queue_command": {"command": "playsound mob.ditsh.vita_mimic.attack @a ~ ~ ~"}}, "ditsh:on_spot_player": {"queue_command": {"command": "playsound mob.ditsh.vita_mimic.spot @a ~ ~ ~"}}, "ditsh:on_player_start_looking": {}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "monster", "mob", "vita_mimic"]}, "minecraft:collision_box": {"width": 0.6, "height": 2.9}, "minecraft:health": {"value": 40, "max": 40}, "minecraft:hurt_on_condition": {"damage_conditions": [{"filters": {"test": "in_lava", "subject": "self", "operator": "==", "value": true}, "cause": "lava", "damage_per_tick": 4}]}, "minecraft:movement": {"value": 0.2}, "minecraft:navigation.walk": {"can_path_over_water": true, "avoid_water": true, "avoid_damage_blocks": true}, "minecraft:movement.basic": {}, "minecraft:jump.static": {}, "minecraft:can_climb": {}, "minecraft:behavior.float": {"priority": 0}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 2.1, "on_kill": {"event": "ditsh:on_kill", "target": "self"}}, "minecraft:attack": {"damage": 7}, "minecraft:behavior.nearest_attackable_target": {"priority": 2, "must_see": true, "reselect_targets": true, "entity_types": [{"filters": {"test": "is_family", "subject": "other", "value": "player"}, "max_dist": 64}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_stroll": {"priority": 4}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:pushable": {}, "minecraft:physics": {}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:on_target_acquired": {"event": "ditsh:on_spot_player", "target": "self"}, "minecraft:environment_sensor": {"triggers": [{"filters": {"all_of": [{"test": "has_target", "subject": "self", "value": true}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": false}]}, "event": "ditsh:start_chase_music"}, {"filters": {"all_of": [{"test": "has_target", "subject": "self", "value": false}, {"test": "bool_property", "subject": "self", "domain": "ditsh:playing_music", "value": true}]}, "event": "ditsh:stop_chase_music"}]}}}}